/**
 * Three.js 渲染服务
 * 管理3D场景的渲染和交互
 */
import * as THREE from 'three';
import { SceneData, SceneNode } from './SceneLoader';
import CameraControlService from './CameraControlService';
import ObjectEditService, { TransformMode, TransformSpace } from './ObjectEditService';
import ThreePerformanceService from './ThreePerformanceService';

export interface RenderOptions {
  antialias: boolean;
  shadows: boolean;
  physicallyCorrectLights: boolean;
  outputColorSpace: THREE.ColorSpace;
}

export interface CameraOptions {
  fov: number;
  near: number;
  far: number;
  position: THREE.Vector3;
  target: THREE.Vector3;
}

class ThreeRenderService {
  private static instance: ThreeRenderService;
  
  private renderer: THREE.WebGLRenderer | null = null;
  private scene: THREE.Scene | null = null;
  private camera: THREE.PerspectiveCamera | null = null;
  private canvas: HTMLCanvasElement | null = null;
  private animationId: number | null = null;
  
  // 场景对象
  private sceneObjects: Map<string, THREE.Object3D> = new Map();
  private selectedObject: THREE.Object3D | null = null;
  
  // 控制器和辅助对象
  private gridHelper: THREE.GridHelper | null = null;
  private axesHelper: THREE.AxesHelper | null = null;
  private ambientLight: THREE.AmbientLight | null = null;
  private directionalLight: THREE.DirectionalLight | null = null;

  // 相机控制器
  private cameraControl: CameraControlService | null = null;

  // 对象编辑服务
  private objectEditService: ObjectEditService | null = null;

  // 性能优化服务
  private performanceService: ThreePerformanceService | null = null;

  // 状态
  private isInitialized = false;
  private showGrid = true;
  private showAxes = true;
  
  public static getInstance(): ThreeRenderService {
    if (!ThreeRenderService.instance) {
      ThreeRenderService.instance = new ThreeRenderService();
    }
    return ThreeRenderService.instance;
  }

  /**
   * 初始化渲染器
   */
  public async initialize(
    canvas: HTMLCanvasElement, 
    options: Partial<RenderOptions> = {}
  ): Promise<void> {
    if (this.isInitialized) {
      console.warn('ThreeRenderService 已经初始化');
      return;
    }

    this.canvas = canvas;
    
    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({
      canvas: canvas,
      antialias: options.antialias ?? true,
      alpha: true
    });
    
    this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.outputColorSpace = options.outputColorSpace ?? THREE.SRGBColorSpace;
    this.renderer.useLegacyLights = !(options.useLegacyLights ?? true);
    
    if (options.shadows) {
      this.renderer.shadowMap.enabled = true;
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    }

    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x2a2a2a);

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(
      75, // fov
      canvas.clientWidth / canvas.clientHeight, // aspect
      0.1, // near
      1000 // far
    );
    this.camera.position.set(5, 5, 5);
    this.camera.lookAt(0, 0, 0);

    // 创建基础光照
    this.setupLighting();
    
    // 创建辅助对象
    this.setupHelpers();
    
    // 设置相机控制器
    this.setupCameraControl();

    // 设置对象编辑服务
    this.setupObjectEditService();

    // 设置性能优化服务
    this.setupPerformanceService();

    // 监听窗口大小变化
    this.setupResizeHandler();

    this.isInitialized = true;
    console.log('ThreeRenderService 初始化完成');

    // 开始渲染循环
    this.startRenderLoop();
  }

  /**
   * 设置光照
   */
  private setupLighting(): void {
    if (!this.scene) return;

    // 环境光
    this.ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    this.scene.add(this.ambientLight);

    // 方向光
    this.directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    this.directionalLight.position.set(10, 10, 5);
    this.directionalLight.castShadow = true;
    this.directionalLight.shadow.mapSize.width = 2048;
    this.directionalLight.shadow.mapSize.height = 2048;
    this.scene.add(this.directionalLight);
  }

  /**
   * 设置辅助对象
   */
  private setupHelpers(): void {
    if (!this.scene) return;

    // 网格辅助
    this.gridHelper = new THREE.GridHelper(20, 20, 0x444444, 0x444444);
    this.gridHelper.visible = this.showGrid;
    this.scene.add(this.gridHelper);

    // 坐标轴辅助
    this.axesHelper = new THREE.AxesHelper(5);
    this.axesHelper.visible = this.showAxes;
    this.scene.add(this.axesHelper);
  }

  /**
   * 设置相机控制器
   */
  private setupCameraControl(): void {
    if (!this.camera || !this.canvas) return;

    this.cameraControl = new CameraControlService();
    this.cameraControl.initialize(this.camera, this.canvas, {
      enableDamping: true,
      dampingFactor: 0.05,
      enableZoom: true,
      enableRotate: true,
      enablePan: true,
      minDistance: 1,
      maxDistance: 100
    });
  }

  /**
   * 设置对象编辑服务
   */
  private setupObjectEditService(): void {
    if (!this.scene || !this.camera || !this.canvas) return;

    this.objectEditService = ObjectEditService.getInstance();
    this.objectEditService.initialize(this.scene, this.camera, this.canvas);

    // 监听选择事件
    this.objectEditService.addSelectionListener((event) => {
      console.log('对象选择事件:', event);
    });

    // 监听变换事件
    this.objectEditService.addTransformListener((object) => {
      console.log('对象变换事件:', object.name);
    });
  }

  /**
   * 设置性能优化服务
   */
  private setupPerformanceService(): void {
    if (!this.renderer || !this.scene || !this.camera) return;

    this.performanceService = ThreePerformanceService.getInstance();
    this.performanceService.initialize(this.renderer, this.scene, this.camera);

    // 监听性能统计更新
    this.performanceService.addStatsUpdateListener((stats) => {
      // 可以在这里处理性能统计更新
      if (stats.fps < 30) {
        console.warn('性能警告：FPS低于30');
      }
    });
  }

  /**
   * 设置窗口大小变化处理
   */
  private setupResizeHandler(): void {
    const handleResize = () => {
      if (!this.canvas || !this.camera || !this.renderer) return;
      
      const width = this.canvas.clientWidth;
      const height = this.canvas.clientHeight;
      
      this.camera.aspect = width / height;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(width, height);
    };

    window.addEventListener('resize', handleResize);
  }

  /**
   * 开始渲染循环
   */
  private startRenderLoop(): void {
    const render = () => {
      // 更新相机控制器
      if (this.cameraControl) {
        this.cameraControl.update();
      }

      // 应用性能优化
      if (this.performanceService) {
        this.performanceService.applyFrustumCulling();
      }

      // 渲染场景
      if (this.renderer && this.scene && this.camera) {
        this.renderer.render(this.scene, this.camera);
      }

      this.animationId = requestAnimationFrame(render);
    };
    render();
  }

  /**
   * 停止渲染循环
   */
  public stopRenderLoop(): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  /**
   * 加载场景数据
   */
  public loadSceneData(sceneData: SceneData): void {
    if (!this.scene) {
      console.error('渲染器未初始化');
      return;
    }

    // 清除现有场景对象
    this.clearSceneObjects();

    console.log('加载场景数据:', sceneData);

    // 创建场景对象
    sceneData.nodes.forEach(node => {
      const object3D = this.createObject3DFromNode(node);
      if (object3D) {
        this.scene!.add(object3D);
        this.sceneObjects.set(node.id, object3D);
      }
    });

    console.log('场景对象创建完成，总数:', this.sceneObjects.size);
  }

  /**
   * 从场景节点创建3D对象
   */
  private createObject3DFromNode(node: SceneNode): THREE.Object3D | null {
    let object3D: THREE.Object3D;

    switch (node.type) {
      case 'mesh':
        object3D = this.createMeshObject(node);
        break;
      case 'light':
        object3D = this.createLightObject(node);
        break;
      case 'camera':
        object3D = this.createCameraObject(node);
        break;
      case 'group':
      case 'empty':
      default:
        object3D = new THREE.Group();
        break;
    }

    // 设置变换
    object3D.position.fromArray(node.transform.position);
    object3D.quaternion.fromArray(node.transform.rotation);
    object3D.scale.fromArray(node.transform.scale);
    object3D.name = node.name;
    object3D.visible = node.visible;

    return object3D;
  }

  /**
   * 创建网格对象
   */
  private createMeshObject(node: SceneNode): THREE.Mesh {
    // 创建基础几何体
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    
    // 创建基础材质
    const material = new THREE.MeshStandardMaterial({
      color: 0x888888,
      metalness: 0.1,
      roughness: 0.8
    });

    const mesh = new THREE.Mesh(geometry, material);
    mesh.castShadow = true;
    mesh.receiveShadow = true;
    
    return mesh;
  }

  /**
   * 创建光源对象
   */
  private createLightObject(node: SceneNode): THREE.Light {
    // 创建点光源作为默认光源
    const light = new THREE.PointLight(0xffffff, 1, 10);
    light.castShadow = true;
    return light;
  }

  /**
   * 创建相机对象
   */
  private createCameraObject(node: SceneNode): THREE.Camera {
    const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
    return camera;
  }

  /**
   * 清除场景对象
   */
  private clearSceneObjects(): void {
    this.sceneObjects.forEach(object => {
      if (this.scene) {
        this.scene.remove(object);
      }
      // 清理几何体和材质
      if (object instanceof THREE.Mesh) {
        object.geometry.dispose();
        if (Array.isArray(object.material)) {
          object.material.forEach(mat => mat.dispose());
        } else {
          object.material.dispose();
        }
      }
    });
    this.sceneObjects.clear();
  }

  /**
   * 切换网格显示
   */
  public setGridVisible(visible: boolean): void {
    this.showGrid = visible;
    if (this.gridHelper) {
      this.gridHelper.visible = visible;
    }
  }

  /**
   * 切换坐标轴显示
   */
  public setAxesVisible(visible: boolean): void {
    this.showAxes = visible;
    if (this.axesHelper) {
      this.axesHelper.visible = visible;
    }
  }

  /**
   * 获取相机
   */
  public getCamera(): THREE.PerspectiveCamera | null {
    return this.camera;
  }

  /**
   * 获取场景
   */
  public getScene(): THREE.Scene | null {
    return this.scene;
  }

  /**
   * 获取渲染器
   */
  public getRenderer(): THREE.WebGLRenderer | null {
    return this.renderer;
  }

  /**
   * 获取对象编辑服务
   */
  public getObjectEditService(): ObjectEditService | null {
    return this.objectEditService;
  }

  /**
   * 设置变换模式
   */
  public setTransformMode(mode: TransformMode): void {
    if (this.objectEditService) {
      this.objectEditService.setTransformMode(mode);
    }
  }

  /**
   * 设置变换空间
   */
  public setTransformSpace(space: TransformSpace): void {
    if (this.objectEditService) {
      this.objectEditService.setTransformSpace(space);
    }
  }

  /**
   * 获取选中对象
   */
  public getSelectedObject(): THREE.Object3D | null {
    return this.objectEditService ? this.objectEditService.getSelectedObject() : null;
  }

  /**
   * 获取性能服务
   */
  public getPerformanceService(): ThreePerformanceService | null {
    return this.performanceService;
  }

  /**
   * 获取性能统计
   */
  public getPerformanceStats() {
    return this.performanceService ? this.performanceService.getStats() : null;
  }

  /**
   * 获取性能建议
   */
  public getPerformanceRecommendations(): string[] {
    return this.performanceService ? this.performanceService.getPerformanceRecommendations() : [];
  }

  /**
   * 销毁渲染器
   */
  public dispose(): void {
    this.stopRenderLoop();
    this.clearSceneObjects();

    // 销毁相机控制器
    if (this.cameraControl) {
      this.cameraControl.dispose();
      this.cameraControl = null;
    }

    // 销毁对象编辑服务
    if (this.objectEditService) {
      this.objectEditService.dispose();
      this.objectEditService = null;
    }

    // 销毁性能优化服务
    if (this.performanceService) {
      this.performanceService.dispose();
      this.performanceService = null;
    }

    if (this.renderer) {
      this.renderer.dispose();
      this.renderer = null;
    }

    this.scene = null;
    this.camera = null;
    this.canvas = null;
    this.isInitialized = false;

    console.log('ThreeRenderService 已销毁');
  }
}

export default ThreeRenderService;
