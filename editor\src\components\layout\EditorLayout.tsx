/**
 * 编辑器布局组件
 */
import React, { useState, useEffect, useRef } from 'react';
import { Layout, Button, Tooltip, Space, Dropdown, Modal, Input, Form } from 'antd';
import {
  FullscreenOutlined,
  FullscreenExitOutlined,
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ArrowsAltOutlined,
  <PERSON>otateRightOutlined,
  ColumnWidthOutlined,
  BorderOutlined,
  DotChartOutlined,
  AppstoreOutlined,
  LayoutOutlined,
  ReloadOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  BranchesOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  setTransformMode,
  setTransformSpace,
  setSnapMode,
  setShowGrid,
  setShowAxes,
  setIsPlaying,
  TransformMode,
  TransformSpace,
  SnapMode,
  undo,
  redo} from '../../store/editor/editorSlice';
import { toggleFullscreen, PanelType } from '../../store/ui/uiSlice';
import {
  setLayout,
  saveLayout,
  loadLayout,
  resetLayout,
  toggleTheme,
  defaultLayout
} from '../../store/ui/layoutSlice';
import {
  ViewportPanelTab,
  HierarchyPanelTab,
  AssetsPanelTab,
  ScenePanelTab,
  InspectorPanelTab,
  PropertiesPanelTab,
  MaterialsPanelTab,
  ConsolePanelTab,
  VisualScriptPanelTab
} from '../panels/PanelTabs';
import { setShowGitPanel } from '../../store/git/gitSlice';

import Toolbar from '../toolbar/Toolbar';
import MenuBar from '../menubar/MenuBar';
import DockLayout, { DockLayoutRef } from './DockLayout';
import MobileAdaptiveLayout from './MobileAdaptiveLayout';
import LayoutService from '../../services/LayoutService';
import MobileDeviceService from '../../services/MobileDeviceService';
import { registerPanelComponent } from '../panels/PanelRegistry';
import ScenePanel from '../panels/ScenePanel';
import HierarchyPanel from '../panels/HierarchyPanel';
import InspectorPanel from '../panels/InspectorPanel';
import AssetsPanel from '../panels/AssetsPanel';
import ConsolePanel from '../panels/ConsolePanel';
import ViewportPanel from '../panels/ViewportPanel';
import CollaborationPanel from '../collaboration/CollaborationPanel';
import UserTestingPanel from '../testing/UserTestingPanel';
import DebugPanel from '../debug/DebugPanel';
import PerformanceOptimizationPanel from '../optimization/PerformanceOptimizationPanel';
import ResourceHotUpdatePanel from '../resources/ResourceHotUpdatePanel';

import { LayoutData } from 'rc-dock';

const { Header, Content } = Layout;

interface EditorLayoutProps {
  projectId: string;
  sceneId: string;
}

// 创建默认布局函数 - 参考原项目ir-engine-dev的defaultLayout函数
// 根据图片要求，右侧面板区域采用左图3D场景的布局设计
const createDefaultLayout = (flags: {
  visualScriptPanelEnabled: boolean;
  activeLowerPanel: string;
}): LayoutData => {
  const tabs = [AssetsPanelTab];
  if (flags.visualScriptPanelEnabled) {
    tabs.push(VisualScriptPanelTab);
  }
  const activeLowerPane = flags.activeLowerPanel;

  return {
    dockbox: {
      mode: 'horizontal',
      children: [
        {
          mode: 'vertical',
          size: 8, // 左侧主要区域，包含3D视口
          children: [
            {
              tabs: [ViewportPanelTab]
            },
            {
              tabs: tabs
            }
          ]
        },
        {
          mode: 'horizontal', // 改为水平布局，模拟3D场景的布局结构
          size: 4, // 右侧面板区域，增加宽度
          children: [
            {
              mode: 'vertical',
              size: 2, // 左侧子区域
              children: [
                {
                  tabs: [HierarchyPanelTab, ScenePanelTab]
                },
                {
                  tabs: [MaterialsPanelTab]
                }
              ]
            },
            {
              mode: 'vertical',
              size: 2, // 右侧子区域，采用3D场景的垂直布局结构
              children: [
                {
                  tabs: [PropertiesPanelTab],
                  activeId: activeLowerPane
                },
                {
                  tabs: [InspectorPanelTab]
                }
              ]
            }
          ]
        }
      ]
    }
  };
};

export const EditorLayout: React.FC<EditorLayoutProps> = ({ projectId, sceneId }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const dockLayoutRef = useRef<DockLayoutRef>(null);

  const {
    transformMode,
    transformSpace,
    snapMode,
    showGrid,
    showAxes,
    isPlaying} = useAppSelector((state) => state.editor);

  const { fullscreen } = useAppSelector((state) => state.ui);
  // 主题和布局改从 layout slice 读取，避免与 ui slice 的旧字段不一致
  const { theme, savedLayouts } = useAppSelector((state) => state.layout);

  const [saveLayoutModalVisible, setSaveLayoutModalVisible] = useState(false);
  const [layoutName, setLayoutName] = useState('');
  const [form] = Form.useForm();

  // 布局配置标志
  const [layoutFlags] = useState({
    visualScriptPanelEnabled: true,
    activeLowerPanel: 'propertiesPanel'
  });

  // UI启用状态
  const [uiEnabled] = useState(true);

  // 记忆化的默认布局
  const memoizedDefaultLayout = React.useMemo(() =>
    createDefaultLayout(layoutFlags),
    [layoutFlags]
  );

  // 初始化布局服务
  useEffect(() => {
    if (dockLayoutRef.current) {
      LayoutService.getInstance().setDockLayoutRef(dockLayoutRef.current);
    }

    // 注册面板组件
    registerPanelComponent(PanelType.HIERARCHY, HierarchyPanel);
    registerPanelComponent(PanelType.INSPECTOR, InspectorPanel);
    registerPanelComponent(PanelType.SCENE, ScenePanel);
    registerPanelComponent(PanelType.ASSETS, AssetsPanel);
    registerPanelComponent(PanelType.CONSOLE, ConsolePanel);
    registerPanelComponent('viewport', ViewportPanel); // 注册视口面板
    registerPanelComponent(PanelType.COLLABORATION, CollaborationPanel);
    registerPanelComponent(PanelType.USER_TESTING, UserTestingPanel);
    registerPanelComponent(PanelType.DEBUG, DebugPanel);
    registerPanelComponent(PanelType.PERFORMANCE_OPTIMIZATION, PerformanceOptimizationPanel);

    // 注册面板内容组件到布局服务
    LayoutService.registerPanelContent(PanelType.HIERARCHY, HierarchyPanel);
    LayoutService.registerPanelContent(PanelType.INSPECTOR, InspectorPanel);
    LayoutService.registerPanelContent(PanelType.SCENE, ScenePanel);
    LayoutService.registerPanelContent(PanelType.ASSETS, AssetsPanel);
    LayoutService.registerPanelContent(PanelType.CONSOLE, ConsolePanel);
    LayoutService.registerPanelContent('viewport', ViewportPanel); // 注册视口面板
    LayoutService.registerPanelContent(PanelType.COLLABORATION, CollaborationPanel);
    LayoutService.registerPanelContent(PanelType.USER_TESTING, UserTestingPanel);
    LayoutService.registerPanelContent(PanelType.DEBUG, DebugPanel);
    LayoutService.registerPanelContent(PanelType.PERFORMANCE_OPTIMIZATION, PerformanceOptimizationPanel);
    LayoutService.registerPanelContent(PanelType.RESOURCE_HOT_UPDATE, ResourceHotUpdatePanel);
  }, [dockLayoutRef]);

  // 切换全屏
  const handleToggleFullscreen = () => {
    dispatch(toggleFullscreen());

    if (!fullscreen) {
      document.documentElement.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  };

  // 切换播放状态
  const handleTogglePlay = () => {
    dispatch(setIsPlaying(!isPlaying));
  };

  // 设置变换模式
  const handleSetTransformMode = (mode: TransformMode) => {
    dispatch(setTransformMode(mode));
  };

  // 设置变换空间
  const handleSetTransformSpace = (space: TransformSpace) => {
    dispatch(setTransformSpace(space));
  };

  // 设置网格捕捉模式
  const handleSetSnapMode = (mode: SnapMode) => {
    dispatch(setSnapMode(mode));
  };

  // 切换网格显示
  const handleToggleGrid = () => {
    dispatch(setShowGrid(!showGrid));
  };

  // 切换坐标轴显示
  const handleToggleAxes = () => {
    dispatch(setShowAxes(!showAxes));
  };

  // 撤销操作
  const handleUndo = () => {
    dispatch(undo());
  };

  // 重做操作
  const handleRedo = () => {
    dispatch(redo());
  };

  // 处理布局变化
  const handleLayoutChange = (newLayout: LayoutData) => {
    dispatch(setLayout(newLayout));
  };

  // 重置布局
  const handleResetLayout = () => {
    dispatch(resetLayout());
    if (dockLayoutRef.current) {
      dockLayoutRef.current.loadLayout(memoizedDefaultLayout);
    }
  };

  // 切换主题
  const handleToggleTheme = () => {
    dispatch(toggleTheme());
  };

  // 打开保存布局对话框
  const handleOpenSaveLayoutModal = () => {
    setLayoutName('');
    form.resetFields();
    setSaveLayoutModalVisible(true);
  };

  // 保存布局
  const handleSaveLayout = () => {
    if (!layoutName.trim()) {
      return;
    }

    if (dockLayoutRef.current) {
      const currentLayout = dockLayoutRef.current.saveLayout() as LayoutData;
      dispatch(saveLayout({ name: layoutName, layout: currentLayout }));
      setSaveLayoutModalVisible(false);
    }
  };

  // 加载布局
  const handleLoadLayout = (layoutName: string) => {
    dispatch(loadLayout(layoutName));
    if (dockLayoutRef.current && savedLayouts[layoutName]) {
      dockLayoutRef.current.loadLayout(savedLayouts[layoutName]);
    }
  };



  // 布局菜单项
  const layoutMenuItems = [
    {
      key: 'reset',
      label: t('editor.layout.reset') || '重置布局',
      icon: <ReloadOutlined />,
      onClick: handleResetLayout
    },
    {
      key: 'save',
      label: t('editor.layout.save') || '保存布局',
      icon: <SaveOutlined />,
      onClick: handleOpenSaveLayoutModal
    },
    {
      key: 'theme',
      label: theme === 'light' ? (t('editor.layout.darkTheme') || '暗色主题') : (t('editor.layout.lightTheme') || '亮色主题'),
      icon: theme === 'light' ? <EyeInvisibleOutlined /> : <EyeOutlined />,
      onClick: handleToggleTheme
    },
    {
      type: 'divider' as const
    },
    {
      key: 'layouts',
      label: t('editor.layout.loadLayout') || '加载布局',
      children: Object.keys(savedLayouts).map(name => ({
        key: `layout-${name}`,
        label: name,
        onClick: () => handleLoadLayout(name)
      }))
    }
  ];

  // 检查是否是移动设备
  const isMobileDevice = MobileDeviceService.isMobileDevice() || MobileDeviceService.isTabletDevice();

  return (
    <main className="pointer-events-auto">
      <div
        id="editor-container"
        className="flex flex-col"
        style={{
          height: '100vh',
          background: 'transparent'
        }}
      >
        {uiEnabled && (
          <>
            {/* 菜单栏 */}
            <div style={{ height: '32px', background: '#2a2a2a', position: 'relative', zIndex: 3 }}>
              <MenuBar />
            </div>

            {/* 工具栏 */}
            <div style={{ height: '40px', background: '#1e1e1e', position: 'relative', zIndex: 2 }}>
              <Toolbar />
            </div>

            {/* 主编辑器区域 */}
            <div className="mt-1 flex overflow-hidden" style={{ height: 'calc(100vh - 72px)' }}>
              <div style={{ position: 'absolute', left: 5, top: 82, right: 5, bottom: 5 }}>
                <DockLayout
                  ref={dockLayoutRef}
                  defaultLayout={memoizedDefaultLayout}
                  onLayoutChange={handleLayoutChange}
                />
              </div>
            </div>
          </>
        )}
      </div>

      {/* 保存布局对话框 */}
      <Modal
        title={t('editor.layout.saveLayout') || '保存布局'}
        open={saveLayoutModalVisible}
        onOk={handleSaveLayout}
        onCancel={() => setSaveLayoutModalVisible(false)}
      >
        <Form form={form}>
          <Form.Item
            label={t('editor.layout.layoutName') || '布局名称'}
            name="layoutName"
            rules={[{ required: true, message: t('editor.layout.nameRequired') || '请输入布局名称' }]}
          >
            <Input
              value={layoutName}
              onChange={(e) => setLayoutName(e.target.value)}
              placeholder={t('editor.layout.enterName') || '请输入布局名称'}
            />
          </Form.Item>
        </Form>
      </Modal>
    </main>
  );
};
