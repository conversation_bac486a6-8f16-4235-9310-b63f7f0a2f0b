/**
 * 编辑器入口文件
 */
import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { store } from './store';
import './styles/index.less';

// 导入i18n配置
import './i18n/index';
import i18n from './i18n/index';

import App from './App';

// 强制设置语言为中文
const initLanguage = async () => {
  try {
    // 等待 i18n 初始化
    if (!i18n.isInitialized) {
      await new Promise((resolve) => {
        if (i18n.isInitialized) {
          resolve(true);
        } else {
          i18n.on('initialized', () => resolve(true));
        }
      });
    }

    // 强制设置为中文
    const savedLang = localStorage.getItem('i18nextLng');
    if (!savedLang || (savedLang !== 'zh-CN' && savedLang !== 'en-US')) {
      await i18n.changeLanguage('zh-CN');
      localStorage.setItem('i18nextLng', 'zh-CN');
    }

    console.log('Language initialized:', i18n.language);
    console.log('Test translation:', i18n.t('common.loading'));
  } catch (error) {
    console.error('Failed to initialize language:', error);
  }
};

// 立即初始化语言
initLanguage();

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Provider store={store}>
      <BrowserRouter>
        <ConfigProvider locale={zhCN}>
          <App />
        </ConfigProvider>
      </BrowserRouter>
    </Provider>
  </React.StrictMode>,
);
